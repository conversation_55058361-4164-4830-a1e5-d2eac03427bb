#!/usr/bin/env python
"""
Демонстрация структуры ответа JWT аутентификации
"""
import os
import sys
import django

# Настройка Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'DjangoBaseApp.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from studio.models import CustomUser
from studio.serializers import LoginSerializer
import json

def demo_jwt_response():
    """Демонстрация ответа JWT аутентификации"""
    
    print("🚀 Демонстрация JWT аутентификации\n")
    
    try:
        # Получаем пользователя
        user = CustomUser.objects.get(username='maksat')
        print(f"👤 Найден пользователь: {user.username}")
        print(f"📧 Email: {user.email}")
        print(f"🎭 Роль: {user.role}")
        print()
        
        # Создаем JWT токены
        refresh = RefreshToken.for_user(user)
        
        # Формируем ответ в требуемом формате
        response_data = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': {
                'user_id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
            }
        }
        
        print("📋 Структура ответа при успешном входе:")
        print("=" * 50)
        print(json.dumps(response_data, indent=2, ensure_ascii=False))
        print("=" * 50)
        print()
        
        # Показываем информацию о токенах
        print("🔑 Информация о токенах:")
        print(f"🔄 Refresh токен (первые 50 символов): {str(refresh)[:50]}...")
        print(f"🎫 Access токен (первые 50 символов): {str(refresh.access_token)[:50]}...")
        print()
        
        # Показываем данные пользователя
        print("👤 Данные пользователя в ответе:")
        user_data = response_data['user']
        for key, value in user_data.items():
            print(f"   {key}: {value}")
        print()
        
        print("✅ JWT аутентификация настроена корректно!")
        print()
        print("📖 Для тестирования API:")
        print("1. Запустите сервер: python manage.py runserver")
        print("2. Отправьте POST запрос на /api/auth/login/ с данными:")
        print('   {"username": "maksat", "password": "admin123"}')
        print("3. Используйте полученный access токен в заголовке:")
        print('   Authorization: Bearer <access_token>')
        
    except CustomUser.DoesNotExist:
        print("❌ Пользователь 'maksat' не найден!")
        print("Создайте пользователя командой: python manage.py createsuperuser")
    except Exception as e:
        print(f"❌ Ошибка: {e}")

def show_api_endpoints():
    """Показать доступные API эндпоинты"""
    print("\n🌐 Доступные API эндпоинты:")
    print("-" * 40)
    
    endpoints = [
        ("POST", "/api/auth/login/", "Вход в систему"),
        ("POST", "/api/auth/token/refresh/", "Обновление токена"),
        ("POST", "/api/auth/token/verify/", "Проверка токена"),
        ("GET", "/api/studio-requests/", "Список заявок студии (защищено)"),
        ("POST", "/api/studio-requests/", "Создание заявки студии (защищено)"),
        ("GET", "/api/school-enrollments/", "Список записей на курсы (защищено)"),
        ("POST", "/api/school-enrollments/", "Создание записи на курсы (защищено)"),
    ]
    
    for method, endpoint, description in endpoints:
        print(f"{method:6} {endpoint:30} - {description}")

# def show_curl_examples():
#     """Показать примеры curl команд"""
#     print("\n💻 Примеры curl команд:")
#     print("-" * 30)
    
#     print("\n1. Вход в систему:")
#     print("""curl -X POST http://127.0.0.1:8000/api/auth/login/ \\
#   -H "Content-Type: application/json" \\
#   -d '{"username": "maksat", "password": "admin123"}'""")
    
#     print("\n2. Доступ к защищенному эндпоинту:")
#     print("""curl -X GET http://127.0.0.1:8000/api/studio-requests/ \\
#   -H "Authorization: Bearer <access_token>\"""")

#     print("\n3. Обновление токена:")
#     print("""curl -X POST http://127.0.0.1:8000/api/auth/token/refresh/ \\
#   -H "Content-Type: application/json" \\
#   -d '{"refresh": "<refresh_token>"}'\""")

# if __name__ == "__main__":
#     demo_jwt_response()
#     show_api_endpoints()
#     show_curl_examples()
#     print("\n🎉 Демонстрация завершена!")
