from django.shortcuts import render, get_object_or_404

# Create your views here.
# views.py

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import StudioRequest, SchoolEnrollment
from .serializers import StudioRequestSerializer, SchoolEnrollmentSerializer

class StudioRequestListView(APIView):
    def get(self, request):
        """Get all studio requests"""
        requests = StudioRequest.objects.all()
        serializer = StudioRequestSerializer(requests, many=True)
        return Response(serializer.data)

    def post(self, request):
        """Create a new studio request"""
        serializer = StudioRequestSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class StudioRequestDetailView(APIView):
    def delete(self, request, pk):
        """Delete a studio request by ID"""
        try:
            studio_request = get_object_or_404(StudioRequest, pk=pk)
            studio_request.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except StudioRequest.DoesNotExist:
            return Response(
                {"error": "Studio request not found"},
                status=status.HTTP_404_NOT_FOUND
            )

class SchoolEnrollmentListView(APIView):
    def get(self, request):
        """Get all school enrollments"""
        enrollments = SchoolEnrollment.objects.all()
        serializer = SchoolEnrollmentSerializer(enrollments, many=True)
        return Response(serializer.data)

    def post(self, request):
        """Create a new school enrollment"""
        serializer = SchoolEnrollmentSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SchoolEnrollmentDetailView(APIView):
    def delete(self, request, pk):
        """Delete a school enrollment by ID"""
        try:
            enrollment = get_object_or_404(SchoolEnrollment, pk=pk)
            enrollment.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except SchoolEnrollment.DoesNotExist:
            return Response(
                {"error": "School enrollment not found"},
                status=status.HTTP_404_NOT_FOUND
            )
