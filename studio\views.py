from django.shortcuts import render

# Create your views here.
# views.py

from rest_framework.views import APIView
from rest_framework.response import Response
from .models import StudioRequest, SchoolEnrollment
from .serializers import StudioRequestSerializer, SchoolEnrollmentSerializer

class StudioRequestListView(APIView):
    def get(self, request):
        requests = StudioRequest.objects.all()
        serializer = StudioRequestSerializer(requests, many=True)
        return Response(serializer.data)

class SchoolEnrollmentListView(APIView):
    def get(self, request):
        enrollments = SchoolEnrollment.objects.all()
        serializer = SchoolEnrollmentSerializer(enrollments, many=True)
        return Response(serializer.data)
