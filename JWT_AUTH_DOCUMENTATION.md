# JWT Аутентификация - Документация

## Обзор

Реализована JWT аутентификация с возвратом токенов и данных пользователя в требуемом формате.

## Структура ответа при входе

```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGci...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGci...",
  "user": {
    "user_id": 13, 
    "username": "maksat", 
    "email": "<EMAIL>", 
    "role": "admin"
  }   
}
```

## API Endpoints

### 1. Вход в систему
- **URL**: `POST /api/auth/login/`
- **Описание**: Аутентификация пользователя и получение JWT токенов
- **Тело запроса**:
```json
{
  "username": "maksat",
  "password": "admin123"
}
```

- **Ответ при успехе (200)**:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGci...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGci...",
  "user": {
    "user_id": 1,
    "username": "maksat",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

- **Ответ при ошибке (400)**:
```json
{
  "non_field_errors": ["Неверные учетные данные."]
}
```

### 2. Обновление токена
- **URL**: `POST /api/auth/token/refresh/`
- **Описание**: Обновление access токена с помощью refresh токена
- **Тело запроса**:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGci..."
}
```

- **Ответ при успехе (200)**:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGci..."
}
```

### 3. Проверка токена
- **URL**: `POST /api/auth/token/verify/`
- **Описание**: Проверка валидности токена
- **Тело запроса**:
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGci..."
}
```

## Использование токенов

Для доступа к защищенным эндпоинтам добавьте заголовок:
```
Authorization: Bearer <access_token>
```

## Настройки JWT

- **Access Token Lifetime**: 60 минут
- **Refresh Token Lifetime**: 7 дней
- **Rotate Refresh Tokens**: Включено
- **Blacklist After Rotation**: Включено

## Модель пользователя

Расширенная модель пользователя с полем роли:

```python
class CustomUser(AbstractUser):
    ROLE_CHOICES = [
        ('admin', 'Администратор'),
        ('manager', 'Менеджер'),
        ('user', 'Пользователь'),
    ]
    
    role = models.CharField(
        max_length=20, 
        choices=ROLE_CHOICES, 
        default='user',
        verbose_name="Роль"
    )
```

## Тестирование

1. Запустите сервер:
```bash
python manage.py runserver
```

2. Используйте тестовый скрипт:
```bash
python test_jwt_auth.py
```

3. Или тестируйте через curl:
```bash
# Вход в систему
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "maksat", "password": "admin123"}'

# Доступ к защищенному эндпоинту
curl -X GET http://127.0.0.1:8000/api/studio-requests/ \
  -H "Authorization: Bearer <access_token>"
```

## Созданные файлы

1. **Модели**: `studio/models.py` - CustomUser модель
2. **Сериализаторы**: `studio/serializers.py` - LoginSerializer, UserSerializer
3. **Views**: `studio/views.py` - LoginView
4. **URLs**: `studio/urls.py`, `DjangoBaseApp/urls.py` - маршруты
5. **Настройки**: `DjangoBaseApp/settings.py` - JWT конфигурация
6. **Тесты**: `test_jwt_auth.py` - тестовый скрипт

## Тестовый пользователь

- **Username**: maksat
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin

## Примечания

- Все защищенные эндпоинты требуют JWT аутентификации
- Refresh токены автоматически ротируются при обновлении
- Старые токены добавляются в черный список
- Роли пользователей: admin, manager, user
