#!/usr/bin/env python
"""
Тестовый скрипт для проверки JWT аутентификации
"""
import requests
import json

# Базовый URL API
BASE_URL = "http://127.0.0.1:8000"

def test_login():
    """Тестирование входа в систему"""
    login_url = f"{BASE_URL}/api/auth/login/"
    
    # Данные для входа
    login_data = {
        "username": "maksat",
        "password": "admin123"
    }
    
    print("🔐 Тестирование входа в систему...")
    print(f"URL: {login_url}")
    print(f"Данные: {json.dumps(login_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(login_url, json=login_data)
        
        print(f"\n📊 Статус ответа: {response.status_code}")
        print(f"📄 Ответ сервера:")
        
        if response.status_code == 200:
            response_data = response.json()
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            # Проверяем структуру ответа
            expected_keys = ['refresh', 'access', 'user']
            for key in expected_keys:
                if key in response_data:
                    print(f"✅ Ключ '{key}' присутствует")
                else:
                    print(f"❌ Ключ '{key}' отсутствует")
            
            # Проверяем структуру объекта user
            if 'user' in response_data:
                user_data = response_data['user']
                expected_user_keys = ['user_id', 'username', 'email', 'role']
                for key in expected_user_keys:
                    if key in user_data:
                        print(f"✅ Ключ user.{key} присутствует: {user_data[key]}")
                    else:
                        print(f"❌ Ключ user.{key} отсутствует")
            
            return response_data
        else:
            print(f"❌ Ошибка: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Ошибка подключения. Убедитесь, что сервер запущен на http://127.0.0.1:8000")
        return None
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return None

def test_token_refresh(refresh_token):
    """Тестирование обновления токена"""
    refresh_url = f"{BASE_URL}/api/auth/token/refresh/"
    
    refresh_data = {
        "refresh": refresh_token
    }
    
    print(f"\n🔄 Тестирование обновления токена...")
    print(f"URL: {refresh_url}")
    
    try:
        response = requests.post(refresh_url, json=refresh_data)
        
        print(f"📊 Статус ответа: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Новый access токен получен")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            return response_data
        else:
            print(f"❌ Ошибка: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return None

def test_protected_endpoint(access_token):
    """Тестирование защищенного эндпоинта"""
    protected_url = f"{BASE_URL}/api/studio-requests/"
    
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    print(f"\n🔒 Тестирование защищенного эндпоинта...")
    print(f"URL: {protected_url}")
    
    try:
        response = requests.get(protected_url, headers=headers)
        
        print(f"📊 Статус ответа: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Доступ к защищенному эндпоинту получен")
            response_data = response.json()
            print(f"📄 Данные: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ Ошибка: {response.text}")
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    print("🚀 Запуск тестов JWT аутентификации\n")
    
    # Тест 1: Вход в систему
    login_result = test_login()
    
    if login_result:
        # Тест 2: Обновление токена
        refresh_result = test_token_refresh(login_result['refresh'])
        
        # Тест 3: Доступ к защищенному эндпоинту
        test_protected_endpoint(login_result['access'])
    
    print("\n🏁 Тесты завершены")
